import logging
import time
import async<PERSON>
import re
import os
import json
from llama_cpp import <PERSON><PERSON><PERSON>
from .config import (
    GEMMA_CPP_MODEL_PATH,
    LLAMA_CPP_N_CTX,
    LLAMA_CPP_N_THREADS,
    LLAMA_CPP_USE_MMAP,
    LLAMA_CPP_USE_MLOCK,
    DECISION_TEMP,
    DECISION_MAX_TOKENS,
    DECISION_CONFIDENCE_THRESHOLD,
    FAST_DECISION_MODE,
    DECISION_TIMEOUT,
    DECISION_MAX_CONTEXT_MESSAGES,
    DECISION_MAX_SPEAKER_HISTORY,
    PROMPT_USER_START,
    PROMPT_USER_END
)
from .llama_cpp_adapter import extract_response_from_llama_cpp_result
from shared_model import call_model_safe

# Import latency tracking
try:
    from .latency_tracker import mark_latency_timestamp
except ImportError:
    # Fallback if latency tracking isn't available
    def mark_latency_timestamp(event_name):
        pass

logger = logging.getLogger(__name__)

# Global dictionary to store decision context for each channel
# Format: {channel_id: {"last_decision": bool, "conversation_state": str}}
decision_sessions = {}

# Use the brain model instead of a separate decision model
def get_decision_model():
    """Get the brain model instance for decision making"""
    # Import here to avoid circular imports
    from llm_brain import get_gemma_cpp_client
    return get_gemma_cpp_client()

# Legacy LM Studio client code removed - now using llama.cpp directly


async def should_respond(text: str, current_speaker_id: str, conversation_history: list, speaker_turn_history: list, is_currently_speaking: bool, channel_id=None, confidence_threshold=None):
    """LLM-powered decision making, focusing on multi-speaker dynamics and avoiding self-interruption.

    Now with stateful conversation tracking to maintain context across messages and confidence scoring.

    Args:
        text: The message text to analyze
        current_speaker_id: ID of the current speaker
        conversation_history: List of recent conversation messages
        speaker_turn_history: List of recent speaker turns
        is_currently_speaking: Whether Luna is currently speaking
        channel_id: Optional channel ID for stateful tracking
        confidence_threshold: Optional override for the confidence threshold (default: DECISION_CONFIDENCE_THRESHOLD from config)
    """
    mark_latency_timestamp("decision_start")
    
    text_lower = text.lower()
    current_speaker_label = f"User_{current_speaker_id}" # Or fetch display name if available

    # Fast path: If Luna is mentioned, she should always respond
    if "luna" in text_lower:
        logger.info("Luna will respond (Fast Path): Direct mention of Luna.")
        # Update session if available
        if channel_id:
            session = decision_sessions.get(channel_id)
            if session is None:
                session = {"last_decision": True, "conversation_state": "ACTIVE"}
                decision_sessions[channel_id] = session
            else:
                session["last_decision"] = True
                session["conversation_state"] = "ACTIVE"
        mark_latency_timestamp("decision_end")
        return True

    # Get or create session for this channel
    session = None
    if channel_id:
        session = decision_sessions.get(channel_id)
        if session is None:
            session = {"last_decision": False, "conversation_state": "IDLE"}
            decision_sessions[channel_id] = session

    # --- Decision Prompt Construction ---
    # Create a concise prompt focused on whether Luna should respond
    # Include recent conversation history and speaker turn information
    
    # Limit conversation history based on config
    recent_history = conversation_history[-DECISION_MAX_CONTEXT_MESSAGES:] if conversation_history else []
    recent_speakers = speaker_turn_history[-DECISION_MAX_SPEAKER_HISTORY:] if speaker_turn_history else []
    
    # Format conversation history
    history_lines = []
    for msg in recent_history:
        role = msg.get('role', 'unknown')
        content = msg.get('content', '')
        # Handle both string and list content formats
        if isinstance(content, list):
            # Extract text from multimodal content
            text_parts = [part.get('text', '') for part in content if part.get('type') == 'text']
            content = ' '.join(text_parts)
        history_lines.append(f"{role}: {content}")
    
    history_str = "\n".join(history_lines)
    
    # Format speaker turn history
    # Extract speaker names from the dictionaries
    speaker_names = []
    for speaker_turn in recent_speakers:
        if isinstance(speaker_turn, dict):
            # Extract display name or user ID
            speaker_name = speaker_turn.get('display_name') or speaker_turn.get('user_id', 'Unknown')
            speaker_names.append(str(speaker_name))
        else:
            # If it's already a string, use it directly
            speaker_names.append(str(speaker_turn))
    speaker_str = ", ".join(speaker_names) if speaker_names else "No recent speakers"
    
    # Determine conversation state
    conversation_state = session["conversation_state"] if session else "UNKNOWN"
    
    # Add special handling for direct mentions
    has_direct_mention = "luna" in text_lower
    
    # Add special handling for questions
    has_question = "?" in text
    
    # Add special handling for commands
    has_command = text.startswith("!")
    
    # Construct the prompt with clear instructions
    prompt = f"""You are Luna's decision-making system. Determine if Luna should respond to the latest message.

CONVERSATION HISTORY:
{history_str}

RECENT SPEAKERS: {speaker_str}

CURRENT SPEAKER: {current_speaker_label}
CONVERSATION STATE: {conversation_state}
LUNA CURRENTLY SPEAKING: {is_currently_speaking}
DIRECT MENTION: {has_direct_mention}
CONTAINS QUESTION: {has_question}
APPEARS TO BE COMMAND: {has_command}

LATEST MESSAGE: {text}

Should Luna respond? Consider:
1. Direct mentions, questions, or commands directed at Luna
2. If Luna is already in an active conversation
3. If someone is directly addressing Luna
4. If Luna has something relevant to contribute
5. Avoid interrupting when Luna is already speaking
6. Avoid repetitive responses

REQUIRED FORMAT: Respond with exactly "YES X%" or "NO X%" where X is confidence 0-100.

Decision:"""

    try:
        # Use the brain model instance for better performance
        model = get_decision_model()
        mark_latency_timestamp("decision_api_start")
        
        # Add debug logging for the prompt
        logger.debug(f"Decision prompt length: {len(prompt)} characters")
        logger.debug(f"Decision prompt preview: {prompt[:200]}...")
        
        loop = asyncio.get_event_loop()
        def run_inference():
            # Use the shared thread-safe wrapper to avoid concurrent GPU access
            return call_model_safe(
                model,
                prompt,
                max_tokens=DECISION_MAX_TOKENS,
                temperature=DECISION_TEMP,
                top_p=0.9,
                stop=[]  # Removed aggressive stop sequences that were cutting off responses
            )
        
        # Add timeout to prevent hanging
        try:
            result = await asyncio.wait_for(
                loop.run_in_executor(None, run_inference),
                timeout=DECISION_TIMEOUT
            )
        except asyncio.TimeoutError:
            logger.error(f"Decision model timed out after {DECISION_TIMEOUT} seconds")
            # Conservative fallback: Only respond if explicitly mentioned
            return has_direct_mention
        
        mark_latency_timestamp("decision_api_end")
        
        # Extract decision text from result
        if isinstance(result, dict) and 'choices' in result:
            decision_text = result['choices'][0].get('text', '').strip()
        elif isinstance(result, str):
            decision_text = result.strip()
        else:
            decision_text = str(result).strip() if result else ""
            
        logger.info(f"Raw Decision Output: '{decision_text}'")
        
        # Handle empty responses
        if not decision_text:
            logger.warning("Decision model returned empty response")
            # Conservative fallback: Only respond if explicitly mentioned
            return has_direct_mention
        
        # Parse the decision and confidence
        decision = None
        confidence = 0
        
        # Clean the decision text
        decision_text_clean = decision_text.strip()
        decision_text_upper = decision_text_clean.upper()
        
        # Pattern 1: Look for "YES" or "NO" at the beginning with optional percentage
        yes_no_match = re.match(r'^(YES|NO)\s*(\d+)?%?', decision_text_upper)
        if yes_no_match:
            decision = yes_no_match.group(1)
            confidence = int(yes_no_match.group(2)) if yes_no_match.group(2) else 75
            logger.info(f"Parsed Decision (YES/NO format): {decision}, Confidence: {confidence}%")
        else:
            # Pattern 2: Look for "Response:" followed by YES/NO and percentage
            response_match = re.search(r'RESPONSE:\s*(YES|NO)\s*(\d+)%?', decision_text_upper)
            if response_match:
                decision = response_match.group(1)
                confidence = int(response_match.group(2))
                logger.info(f"Parsed Decision (response format): {decision}, Confidence: {confidence}%")
            else:
                # Pattern 3: Look for YES/NO anywhere with optional percentage
                yes_match = re.search(r'YES\s*(\d+)?%?', decision_text_upper)
                no_match = re.search(r'NO\s*(\d+)?%?', decision_text_upper)
                
                if yes_match and no_match:
                    # Both found, take the last one
                    yes_pos = yes_match.start()
                    no_pos = no_match.start()
                    if yes_pos > no_pos:
                        decision = "YES"
                        confidence = int(yes_match.group(1)) if yes_match.group(1) else 75
                    else:
                        decision = "NO"
                        confidence = int(no_match.group(1)) if no_match.group(1) else 75
                elif yes_match:
                    decision = "YES"
                    confidence = int(yes_match.group(1)) if yes_match.group(1) else 75
                elif no_match:
                    decision = "NO"
                    confidence = int(no_match.group(1)) if no_match.group(1) else 75
                else:
                    # Pattern 4: Ultimate fallback - just look for YES/NO keywords
                    if "YES" in decision_text_upper:
                        decision = "YES"
                        confidence = 50  # Low confidence fallback
                    elif "NO" in decision_text_upper:
                        decision = "NO"
                        confidence = 50  # Low confidence fallback
                    
                logger.info(f"Parsed Decision (flexible): {decision}, Confidence: {confidence}%")

        # Final fallback if nothing was parsed
        if decision is None:
            logger.warning(f"Could not parse decision with confidence from: '{decision_text}'")
            decision = "NO"  # Conservative fallback
            confidence = 0   # Zero confidence for unparseable responses
            logger.info(f"Using ultimate fallback decision: {decision}, Confidence: {confidence}%")

        # Use provided threshold or fall back to config value
        current_threshold = confidence_threshold if confidence_threshold is not None else DECISION_CONFIDENCE_THRESHOLD

        # Determine if Luna should respond based on decision and confidence threshold
        should_luna_respond = (decision == "YES" and confidence >= current_threshold)

        # Log the confidence-based decision
        if decision == "YES":
            if confidence >= current_threshold:
                logger.info(f"Luna will respond (Decision: YES, Confidence: {confidence}% ≥ Threshold: {current_threshold}%)")
            else:
                logger.info(f"Luna will NOT respond despite YES decision (Confidence: {confidence}% < Threshold: {current_threshold}%)")
        else:
            logger.info(f"Luna will NOT respond (Decision: NO, Confidence: {confidence}%)")

        # Override decision if Luna is directly mentioned - she should always respond
        if has_direct_mention:
            logger.info("Luna will respond (Override): Direct mention detected")
            should_luna_respond = True
            # Update session for direct mention
            if session:
                session["last_decision"] = True
                session["conversation_state"] = "ACTIVE"
        
        # Update session with the decision
        if session:
            session["last_decision"] = should_luna_respond
            if should_luna_respond:
                session["conversation_state"] = "ACTIVE"
            elif session["conversation_state"] == "ACTIVE":
                # Only transition from ACTIVE to IDLE if we've been active
                session["conversation_state"] = "IDLE"

        mark_latency_timestamp("decision_end")
        return should_luna_respond

    except Exception as e:
       logger.error(f"Unexpected error in should_respond LLM logic: {e}", exc_info=True)
       mark_latency_timestamp("decision_end")
       # Safer fallback: Only respond if explicitly mentioned on error
       return "luna" in text_lower