import os
import logging
import re
import discord
import time
import asyncio
import json
import aiohttp
from datetime import datetime
import hashlib
from functools import lru_cache
from typing import Dict, List, Optional

# Import necessary components from other modules within the package
from .config import (
    TRANSCRIPT_LOG_CHANNEL_ID, CALL_NOTIFICATION_CHANNEL_ID_STR,
    MAX_HISTORY_MESSAGES, LM_STUDIO_MODEL_NAME, OLLAMA_MODEL_NAME,
    DEFAULT_TEMP, TEMP_PARTICIPATION_INCREMENT, MAX_TEMP, MAX_OUTPUT_TOKENS, TOP_P,
    DM_MAX_OUTPUT_TOKENS, DM_TEMP, PROMPT_LOG_CHANNEL_ID, FAST_SESSION_LIMIT, MAX_STORED_MESSAGES,
    get_id_from_alias, get_main_name_by_id, get_main_name_from_alias, is_minecraft_mode_enabled, # Import the nickname functions and is_minecraft_mode_enabled

    PROMPT_MEMORY_CONTEXT_HEADER, PROMPT_SCREEN_CONTEXT_HEADER,
    PROMPT_IMAGE_ANALYSIS_HEADER, PROMPT_BRAIN_CONTEXT_HEADER
)
from .db_logger import log_message_to_db
from .initialization import get_lm_studio_client, get_ollama_client, get_llama_cpp_client
from .decision import should_respond
from .rag_utils import rewrite_query_for_rag, format_conversation_history_for_prompt
# Import command functions carefully
from .commands import handle_screenshot_command, generate_call_message
# Import latency tracking
from .latency_tracker import mark_latency_timestamp, set_response_length, start_latency_tracking


# Import necessary components from outside the package
# Thread-safe llama.cpp wrappers
from shared_model import call_model_safe, stream_model_safe

# Other external helpers
from text_to_speech import StreamingSpeechPlayer
from utils import remove_emojis, format_datetime
# from discord_sink import _find_user_from_identifier # Removed incorrect import
from utils import find_user_from_identifier # Import the refactored helper function
import llm_brain # Import the brain module
# Import auto-screenshot module
try:
    from . import auto_screenshot
    _auto_screenshot_available = True
except ImportError as e:
    log.warning(f"Could not import auto_screenshot module: {e}")
    auto_screenshot = None
    _auto_screenshot_available = False

# DEBUG: Print the model configuration when module loads
print(f"🎯 DEBUG: OLLAMA_MODEL_NAME loaded as: '{OLLAMA_MODEL_NAME}'")
logger = logging.getLogger(__name__)

# --- Latency Logging Helper ---
async def log_latency_to_discord(bot, message: str):
    """Sends a latency log message to the designated Discord channel."""
    try:
        # Use PROMPT_LOG_CHANNEL_ID for latency logs as well
        log_channel = bot.get_channel(PROMPT_LOG_CHANNEL_ID)
        if log_channel:
            # Use create_task to avoid blocking
            asyncio.create_task(log_channel.send(f"⏱️ LATENCY: {message}"))
        else:
            logger.warning(f"Latency log channel {PROMPT_LOG_CHANNEL_ID} not found.")
    except Exception as e:
        logger.error(f"Error sending latency log to Discord: {e}", exc_info=True)

# --- Prompt Logging Helper ---
async def log_prompt_to_discord(bot, prompt: str, model: str, context_info: str = ""):
    """Logs the complete prompt being sent to llama.cpp to Discord for debugging."""
    try:
        log_channel = bot.get_channel(PROMPT_LOG_CHANNEL_ID)
        if log_channel:
            timestamp = datetime.now().strftime("%H:%M:%S")
            
            # Create a nicely formatted header
            header = f"🤖 **LLAMA.CPP DIRECT CALL** • `{timestamp}`"
            model_info = f"**Model:** `{model}`"
            params_info = f"**Parameters:** {context_info}" if context_info else ""
            
            # Count conversation turns for context
            lines = prompt.split('\n')
            user_lines = [line for line in lines if line.startswith('User: ')]
            assistant_lines = [line for line in lines if line.startswith('Assistant: ')]
            
            conversation_stats = f"**Conversation:** {len(user_lines)} user messages, {len(assistant_lines)} assistant messages"
            
            # Format the prompt with better readability
            formatted_prompt = prompt.replace('User: ', '**👤 User:** ').replace('Assistant:', '**🤖 Assistant:**')
            
            # Handle long prompts by splitting into multiple messages if needed
            max_prompt_length = 1400  # Leave room for headers and formatting
            
            if len(formatted_prompt) <= max_prompt_length:
                # Single message
                full_message = f"{header}\n{model_info}\n{params_info}\n{conversation_stats}\n\n```\n{formatted_prompt}\n```"
                asyncio.create_task(log_channel.send(full_message))
            else:
                # Split into multiple messages
                asyncio.create_task(log_channel.send(f"{header}\n{model_info}\n{params_info}\n{conversation_stats}"))
                
                # Split prompt into chunks
                prompt_lines = formatted_prompt.split('\n')
                current_chunk = ""
                chunk_num = 1
                
                for line in prompt_lines:
                    if len(current_chunk + line + '\n') > max_prompt_length:
                        if current_chunk:
                            asyncio.create_task(log_channel.send(f"```\n{current_chunk.strip()}\n```\n**📄 Part {chunk_num}**"))
                            chunk_num += 1
                            current_chunk = line + '\n'
                        else:
                            # Single line is too long, truncate it
                            truncated_line = line[:max_prompt_length-50] + "... [LINE TRUNCATED]"
                            asyncio.create_task(log_channel.send(f"```\n{truncated_line}\n```\n**📄 Part {chunk_num}**"))
                            chunk_num += 1
                    else:
                        current_chunk += line + '\n'
                
                # Send final chunk
                if current_chunk.strip():
                    asyncio.create_task(log_channel.send(f"```\n{current_chunk.strip()}\n```\n**📄 Final Part**"))
                
        else:
            logger.warning(f"Prompt log channel {PROMPT_LOG_CHANNEL_ID} not found.")
    except Exception as e:
        logger.error(f"Error sending prompt log to Discord: {e}", exc_info=True)

# Helper function to get the next item from a blocking iterator, handling StopIteration
def _get_next_chunk_or_none(iterator):
    """Calls next() on the iterator, returning None if StopIteration is raised."""
    try:
        return next(iterator)
    except StopIteration:
        return None
    except Exception as e:
        # Log other potential errors from the iterator itself
        logger.error(f"Error getting next chunk from iterator: {e}", exc_info=True)
        return None # Treat other errors as end-of-stream for safety

# Global ultra-fast llama.cpp client
_ultra_fast_llama_cpp = None
_llama_cpp_lock = asyncio.Lock()

async def _get_ultra_fast_llama_cpp():
    """Get or create the global ultra-fast llama.cpp client"""
    global _ultra_fast_llama_cpp
    async with _llama_cpp_lock:
        if _ultra_fast_llama_cpp is None:
            _ultra_fast_llama_cpp = UltraFastLlamaCpp()
            await _ultra_fast_llama_cpp.initialize()
    return _ultra_fast_llama_cpp

class UltraFastLlamaCpp:
    def __init__(self):
        self.client = None
        self._model_loaded = False
        self._lock = asyncio.Lock()
        
    async def initialize(self):
        """Initialize the llama.cpp client"""
        if self.client is not None:
            return True
            
        try:
            # Get the llama.cpp client from initialization
            self.client = get_llama_cpp_client()
            if self.client is not None:
                self._model_loaded = True
                logger.info(f"✅ llama.cpp client initialized and ready")
                return True
            else:
                logger.error(f"❌ Failed to get llama.cpp client")
                return False
        except Exception as e:
            logger.error(f"❌ Error initializing llama.cpp client: {e}")
            return False
    
    async def ensure_model_loaded(self, model: str) -> bool:
        """Ensure model is loaded - llama.cpp models are loaded on initialization"""
        if self._model_loaded and self.client is not None:
            return True
            
        logger.info(f"🔥 Ensuring {model} is loaded in llama.cpp...")
        
        try:
            # Re-initialize if needed
            success = await self.initialize()
            if success:
                logger.info(f"✅ Model {model} is loaded and ready in llama.cpp")
                return True
            else:
                logger.warning(f"❌ Failed to ensure model is loaded")
                return False
        except Exception as e:
            logger.error(f"❌ Error ensuring model loaded: {e}")
            return False

# Legacy alias for compatibility
UltraFastOllama = UltraFastLlamaCpp
_get_ultra_fast_ollama = _get_ultra_fast_llama_cpp

# Ultra-optimized llama.cpp functions for direct inference
async def _ultra_fast_llama_cpp_streaming_call(messages, model, temperature=1.0, max_tokens=300, top_p=0.9, bot=None):
    """Ultra-fast streaming using llama.cpp directly with a single structured prompt."""
    llama_cpp_client = await _get_ultra_fast_llama_cpp()
    
    # Ensure model is loaded (should already be)
    await llama_cpp_client.ensure_model_loaded(model)
    
    # Format messages for llama.cpp with system prompt integration
    from .llama_cpp_adapter import format_messages_for_llama_cpp
    prompt = format_messages_for_llama_cpp(messages, include_system_prompt=True)
    
    # Log the complete prompt to Discord for debugging
    if bot:
        context_info = f"Temp: {temperature}, Max Tokens: {max_tokens}, Top-P: {top_p}, STREAMING"
        await log_prompt_to_discord(bot, prompt, model, context_info)
    
    try:
        # Run the synchronous llama.cpp call in a thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        
        def run_inference():
            # Use global streaming wrapper to ensure single GPU access
            return stream_model_safe(
                llama_cpp_client.client,
                prompt,
                max_tokens=max_tokens,
                temperature=temperature,
                top_p=top_p,
                repeat_penalty=1.1,
            )
        
        # Execute in thread pool
        stream = await loop.run_in_executor(None, run_inference)
        
        # Yield chunks from the stream
        for chunk in stream:
            # llama.cpp streaming returns dict with 'choices' containing delta text
            if isinstance(chunk, dict) and 'choices' in chunk and len(chunk['choices']) > 0:
                choice = chunk['choices'][0]
                if 'text' in choice:
                    token = choice['text']
                elif 'delta' in choice and 'content' in choice['delta']:
                    token = choice['delta']['content']
                else:
                    continue
                    
                if token:
                    yield token
                    
    except Exception as e:
        logger.error(f"❌ llama.cpp streaming generation error: {e}")
        raise

async def _ultra_fast_llama_cpp_non_streaming_call(messages, model, temperature=0.7, max_tokens=300, top_p=0.9, bot=None):
    """Ultra-fast non-streaming using llama.cpp directly with a single structured prompt."""
    llama_cpp_client = await _get_ultra_fast_llama_cpp()
    
    await llama_cpp_client.ensure_model_loaded(model)
    
    # Format messages for llama.cpp with system prompt integration
    from .llama_cpp_adapter import format_messages_for_llama_cpp
    prompt = format_messages_for_llama_cpp(messages, include_system_prompt=True)

    if bot:
        context_info = f"Temp: {temperature}, Max Tokens: {max_tokens}, Top-P: {top_p}, NON-STREAMING"
        await log_prompt_to_discord(bot, prompt, model, context_info)

    try:
        # Run the synchronous llama.cpp call in a thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        
        def run_inference():
            # Thread-safe non-streaming call
            return call_model_safe(
                llama_cpp_client.client,
                prompt,
                max_tokens=max_tokens,
                temperature=temperature,
                top_p=top_p,
                repeat_penalty=1.1,
                stream=False
            )
        
        # Execute in thread pool
        result = await loop.run_in_executor(None, run_inference)
        
        # Extract the generated text using adapter
        from .llama_cpp_adapter import extract_response_from_llama_cpp_result
        return extract_response_from_llama_cpp_result(result)
            
    except Exception as e:
        logger.error(f"❌ llama.cpp non-streaming generation error: {e}")
        return ""

# Legacy function names for compatibility  
_ultra_fast_ollama_streaming_call = _ultra_fast_llama_cpp_streaming_call
_ultra_fast_ollama_non_streaming_call = _ultra_fast_llama_cpp_non_streaming_call

# Backward compatibility aliases
_optimized_ollama_streaming_call = _ultra_fast_ollama_streaming_call
_optimized_ollama_non_streaming_call = _ultra_fast_ollama_non_streaming_call





# --- Main Message Processing Function ---



async def process_user_message(
    *, # Force keyword arguments
    bot, # Required: bot instance from caller
    text: str, # Required: message content
    user_id: int, # Required: author ID
    conversation_history: list, # Required: list of message dicts for context
    system_prompt: str, # Required: system prompt string
    sink=None, # Optional: DiscordSink instance (indicates voice context)
    force_respond=False,
    display_name=None,
    text_channel=None, # Optional but needed for text output if sink is None
    image_analysis_text=None, # Optional: Text analysis of an image attached to the message
    chat_session = None, # Optional: Stateful chat session from sink
    # --- New parameters for DM handling ---
    dm_target_user: discord.User | discord.Member | None = None,
    dm_topic: str | None = None,
    # --- Latency Timestamps ---
    audio_detected_time: float | None = None, # Time first audio packet was received for this turn
    transcription_end_time: float | None = None, # Time transcription finished for the triggering utterance
    brain_decision_context: str | None = None, # Optional: Context from the LLM Brain's decision
    mc_bridge=None  # <-- Add this parameter
):
    # Memory client debug removed - memory system disabled
    process_start_time = time.monotonic() # Start timing this function
    # Ensure conversation_history is a list, even if None was passed
    if conversation_history is None:
        conversation_history = []
    # If conversation_history is empty and chat_session is present, reconstruct from chat_session
    if not conversation_history and chat_session and isinstance(chat_session, dict) and "messages" in chat_session:
        conversation_history = [
            {"role": msg.get("role", "user"), "content": msg.get("content", "")}
            for msg in chat_session["messages"]
        ]

    try: # Outer try for the whole function
        # Mark process start timestamp (only if not already marked by voice context)
        mark_latency_timestamp("process_start", process_start_time)
        
        # Pause auto-screenshots during response generation to avoid overhead
        if _auto_screenshot_available and auto_screenshot:
            auto_screenshot.auto_screenshot_manager.pause_for_response()
        
        # Determine context and essential variables
        is_voice_context = sink is not None
        effective_text_channel = text_channel or (sink.text_channel if sink else None)
        effective_display_name = display_name or (f"User_{user_id}" if not sink else "Unknown") # Simplified fallback
        if sink and hasattr(sink, 'get_display_name'): # Check if sink has the method
             effective_display_name = sink.get_display_name(user_id) # Get name via sink if possible

        logger.info(f"Processing {'voice' if is_voice_context else 'text'} message from {effective_display_name}: {text[:30]}...")
        
        # For text context, start latency tracking if not already started
        if not is_voice_context:
            turn_id = start_latency_tracking(
                user_id=str(user_id),
                user_name=effective_display_name
            )
            # Mark transcription end as now for text (immediate)
            mark_latency_timestamp("transcription_end", process_start_time)

        # --- DM Generation Logic (Handles DM requests before normal flow) ---
        if dm_target_user and dm_topic:
            logger.info(f"Initiating DM generation for {dm_target_user.name} ({dm_target_user.id}) about: {dm_topic}")
            dm_handled = False # Flag to ensure we return after this block
            ollama_client = get_ollama_client() # Use Ollama for DM generation
            try:
                # Construct a prompt specifically for generating the DM content
                dm_generation_prompt = f"""
                {system_prompt}

                ---
                You need to send a Direct Message (DM) to the user '{dm_target_user.display_name}'.
                The intended topic or reason for this DM is: "{dm_topic}".

                Based on this topic, the recent conversation history below, and your personality, generate a short, natural-sounding DM to send to '{dm_target_user.display_name}'.
                Keep it concise and casual, like a real person would send a quick message. Avoid overly formal language or sounding like a generic bot notification.
                Focus on the topic: "{dm_topic}". Make sure the message makes sense being sent as a DM.

                Recent Conversation History (for context):
                {format_conversation_history_for_prompt(conversation_history)}

                Generate ONLY the DM message content below:
                """

                # Get or create DM chat session for statefulness
                dm_session_key = f"dm_{dm_target_user.id}"
                dm_session = bot.chat_sessions.get(dm_session_key)

                if dm_session is None:
                    logger.info(f"Creating new DM chat session for user {dm_target_user.id}")
                    dm_session = {
                        "id": f"dm_{dm_target_user.id}_{int(time.time())}",
                        "messages": []  # No system prompt for Ollama - using custom modelfile instead
                    }
                    bot.chat_sessions[dm_session_key] = dm_session

                # Use the Ollama client if available
                if not ollama_client:
                     logger.error(f"Cannot generate DM: Ollama client not initialized.")
                     if effective_text_channel:
                         await effective_text_channel.send(f"❌ Error: Could not generate DM for {dm_target_user.mention}, internal setup missing.")
                     dm_handled = True

                else: # Proceed if Ollama client exists
                    dm_generation_start_time = time.monotonic()
                    logger.info("Sending DM generation request to Ollama...")

                    # Simplified prompt for DM generation
                    simple_dm_prompt = f"You need to send a DM about: '{dm_topic}'. Generate a short, casual message:"

                    # Use the DM session messages and add the new prompt
                    messages = dm_session["messages"].copy()
                    messages.append({"role": "user", "content": simple_dm_prompt})

                    # Make the optimized API call using direct aiohttp
                    dm_content = await _optimized_ollama_non_streaming_call(
                        messages=messages,
                        model=OLLAMA_MODEL_NAME,
                        temperature=DM_TEMP,
                        max_tokens=DM_MAX_OUTPUT_TOKENS,
                        bot=bot
                    )

                    dm_generation_end_time = time.monotonic()
                    logger.info(f"OLLAMA DM GENERATION API CALL: Completed in {dm_generation_end_time - dm_generation_start_time:.6f}s")

                    if not dm_content or not dm_content.strip():
                         logger.warning(f"LLM DM generation failed or was empty.")
                         if effective_text_channel:
                             await effective_text_channel.send(f"⚠️ Tried to DM {dm_target_user.mention} about '{dm_topic}', but couldn't generate content.")
                         dm_handled = True

                    else: # Proceed only if generation was successful
                        dm_content = dm_content.strip()

                        if not dm_content:
                            logger.warning(f"LLM generated empty DM content for topic: {dm_topic}")
                            if effective_text_channel:
                                await effective_text_channel.send(f"⚠️ Tried to DM {dm_target_user.mention} about '{dm_topic}', but couldn't think of what to say!")
                            dm_handled = True

                        else: # Proceed only if content is not empty
                            logger.info(f"Generated DM content for {dm_target_user.name}: {dm_content}")

                            # Update DM session with user prompt and assistant response
                            dm_session["messages"].append({"role": "user", "content": simple_dm_prompt})
                            dm_session["messages"].append({"role": "assistant", "content": dm_content})

                            await dm_target_user.send(dm_content)
                            logger.info(f"Successfully sent generated DM to {dm_target_user.name}")
                            log_message_to_db(
                                user_id=str(bot.user.id), role="assistant_dm",
                                content=f"Sent DM to {dm_target_user.name} ({dm_target_user.id}) about '{dm_topic}': {dm_content}",
                                timestamp=time.time(), channel_type="dm", channel_id=str(dm_target_user.id)
                            )
                            dm_handled = True # Mark DM as handled successfully

            except discord.Forbidden as e:
                logger.warning(f"Failed to send DM to {dm_target_user.name}: Permissions or privacy settings. {e}")
                if effective_text_channel: await effective_text_channel.send(f"⚠️ Couldn't send DM to {dm_target_user.mention} (maybe privacy settings?).")
                dm_handled = True
            except discord.HTTPException as e:
                logger.error(f"Failed to send DM to {dm_target_user.name} due to API error: {e}")
                if effective_text_channel: await effective_text_channel.send(f"❌ Failed to send DM to {dm_target_user.mention} due to a Discord error.")
                dm_handled = True
            except Exception as e:
                logger.error(f"Unexpected error during DM generation/sending for {dm_target_user.name}: {e}", exc_info=True)
                if effective_text_channel: await effective_text_channel.send(f"❌ An unexpected error occurred while trying to generate/send a DM to {dm_target_user.mention}.")
                dm_handled = True
            finally:
                if dm_handled:
                    logger.debug("DM handling block finished, returning.")
                    return # Exit process_user_message if DM was handled (or attempted)

        # --- "Call User" command handling ---
        call_match = re.search(r"luna,? call (\w+)(?: on discord)?", text, re.IGNORECASE)
        # --- "Call User" command logic moved to llm_brain.py ---
        # The brain now decides if a call is needed based on the prompt and executes it.
        # This specific regex check and execution block is removed from processing.py.

        # --- Screenshot command handling ---
        screenshot_patterns = [
            r"take a screenshot", r"capture (?:my|the) screen", r"screenshot",
            r"show me what you see", r"analyze my screen", r"look at my screen",
            r"what(?:'s| is) on my screen"
        ]
        if any(re.search(pattern, text.lower()) for pattern in screenshot_patterns):
            logger.info(f"Screenshot command detected: {text}")
            result = await handle_screenshot_command(sink, text, user_id, effective_display_name, effective_text_channel) # Use function from commands.py
            return result # Return whether screenshot was handled

        # --- Decision Phase ---
        recent_raw_history = sorted(conversation_history, key=lambda x: x.get("timestamp", 0))[-MAX_HISTORY_MESSAGES:]
        speaker_turn_history = sink.get_recent_speaker_turns(count=5) if is_voice_context else []
        is_direct_address = "luna" in text.lower()
        should_force_respond = force_respond or is_direct_address
        should_luna_respond = False

        if should_force_respond:
            logger.info(f"Decision: Forcing response due to direct address or flag.")
            should_luna_respond = True
        else:
            is_luna_speaking = sink.is_speaking if is_voice_context else False
            # Get channel_id for stateful decision tracking
            channel_id = None
            if is_voice_context and sink.voice_client and sink.voice_client.channel:
                channel_id = sink.voice_client.channel.id
            elif effective_text_channel:
                channel_id = effective_text_channel.id

            logger.info(f"Checking 'should_respond'. Luna speaking state: {is_luna_speaking}, Channel ID: {channel_id}")
            should_luna_respond = await should_respond( # Use function from decision.py
                text=text,
                current_speaker_id=str(user_id),
                conversation_history=recent_raw_history,
                speaker_turn_history=speaker_turn_history,
                is_currently_speaking=is_luna_speaking,
                channel_id=channel_id,
                confidence_threshold=None  # Use default from config
            )

        if not should_luna_respond:
            logger.info("Decision: Luna will not respond.")
            return False # Indicate not handled, exit early

        # --- Brain Action Check ---
        # If Luna should respond, first check if the brain wants to take a specific action
        logger.info("Consulting LLM Brain for potential actions...")
        # Mark brain start for text processing (voice already marked in discord_sink.py)
        if not is_voice_context:
            mark_latency_timestamp("brain_start")
        brain_prompt = ""
        if is_voice_context:
            # Construct voice prompt for brain
            vc_name = sink.voice_client.channel.name if sink and sink.voice_client and sink.voice_client.channel else "Unknown VC"
            vc_id = sink.voice_client.channel.id if sink and sink.voice_client and sink.voice_client.channel else "Unknown VC ID"
            turn_history = sink.get_recent_speaker_turns(count=5) # Get recent turns for context

            # Use the canonical names from the turn history
            turn_lines = []
            for turn in turn_history:
                # Get the canonical name for this user
                display_name = turn['display_name']
                turn_lines.append(f"{display_name} ({turn['user_id']}): {turn['text']}")

            brain_prompt = f"Conversation turn in voice channel '{vc_name}' ({vc_id}):\n"
            brain_prompt += "\n".join(turn_lines)
            brain_prompt += "\nBased on this conversation turn, what action should I take?"
        elif effective_text_channel:
            # Construct text prompt for brain
            # Handle different channel types (DMChannel doesn't have a name attribute)
            if hasattr(effective_text_channel, 'name'):
                channel_name = effective_text_channel.name
                channel_description = f"'#{channel_name}'"
            else:
                # This is likely a DMChannel
                channel_name = "DM"
                channel_description = "DM"

            channel_id = effective_text_channel.id
            brain_prompt = f"User '{effective_display_name}' ({user_id}) in channel {channel_description} ({channel_id}) said: '{text}'"
            if image_analysis_text:
                brain_prompt += f"\nImage Analysis: {image_analysis_text}"
            brain_prompt += "\nBased on this, what action should I take?"
        else:
            logger.warning("Cannot construct brain prompt: No voice or text channel context.")
            # Proceed without brain action check? Or return error? Let's proceed for now.

        brain_action_details = None
        if brain_prompt:
            try:
                brain_action_details = await llm_brain.process_brain_request(
                    prompt=brain_prompt,
                    bot=bot,
                    sink=sink, # Pass sink for context if needed by brain actions
                    system_prompt=system_prompt, # Pass main system prompt for context
                    effective_text_channel=effective_text_channel
                )
            except Exception as brain_err:
                logger.error(f"Error calling LLM Brain: {brain_err}", exc_info=True)
                brain_action_details = {'action': 'error', 'details': f"Exception during brain call: {brain_err}"}

        # Mark brain end for text processing (voice already marked in discord_sink.py)
        if not is_voice_context:
            mark_latency_timestamp("brain_end")

        # Handle Brain Action Result
        if brain_action_details:
            action_taken = brain_action_details.get('action', 'error')
            logger.info(f"Brain Action Result: {action_taken}, Details: {brain_action_details}")

        # --- Handle Specific Brain Actions ---
        dm_target_identifier_from_brain = None
        dm_topic_from_brain = None
        dm_exact_message_from_brain = None

        if brain_action_details:

            if action_taken == 'call_user' or action_taken == 'disconnect_user':
                 # These actions are fully executed by the brain function
                 if brain_action_details.get('success', False):
                      logger.info(f"Brain successfully executed action: {action_taken}. Turn processing complete.")
                      return True # Indicate message was handled by the brain action
                 else:
                      logger.warning(f"Brain action '{action_taken}' failed execution. Raw: {brain_action_details.get('raw_decision')}")
                      # Action was attempted but failed, stop further processing.
                      # Brain function should have sent any necessary error message.
                      return False # Indicate message handling failed / stopped

            elif action_taken == 'dm_user':
                 # DM action is only parsed by the brain, execution happens later here.
                 if brain_action_details.get('success', False): # Success here means parsing succeeded
                      logger.info("Brain decided 'DM User'. Storing details for DM generation.")
                      dm_target_identifier_from_brain = brain_action_details.get('target')
                      dm_topic_from_brain = brain_action_details.get('topic')
                      dm_exact_message_from_brain = brain_action_details.get('exact_message')
                      # DO NOT return here. Let processing continue to DM generation phase.
                      # We might want to add the raw decision as context for the main LLM?
                      # brain_decision_context = brain_action_details.get('raw_decision') # Example
                 else:
                      # Should not happen if success is True, but handle defensively
                      logger.error(f"Brain action 'dm_user' indicated success=False unexpectedly. Raw: {brain_action_details.get('raw_decision')}")
                      # Proceed with standard response as fallback.

            elif action_taken == 'pokemon_info':
                logger.info("Brain decided 'Pokemon Info'. Will retrieve Pokemon context and proceed with response.")
                # Context retrieval happens later in the RAG section
            
            elif action_taken == 'type_effectiveness':
                logger.info("Brain decided 'Type Effectiveness'. Will retrieve type chart context and proceed with response.")
                # Context retrieval happens later in the RAG section
            
            elif action_taken == 'no_action':
                logger.info("Brain decided 'No Action'. Proceeding with standard response generation.")
                # Optionally, pass brain's reasoning (raw_decision) as context?
                # brain_decision_context = brain_action_details.get('raw_decision') # Example
            elif action_taken == 'error':
                logger.error(f"Brain processing resulted in an error: {brain_action_details.get('details')}. Raw: {brain_action_details.get('raw_decision')}")
                # Proceed with standard response as a fallback
            else:
                 logger.warning(f"Unknown action returned from brain: {action_taken}. Proceeding with standard response.")

        # --- If brain didn't handle it, proceed with RAG and standard response generation ---
        # --- Pokémon RAG Context (if requested by brain) ---
        pokemon_context = None
        if brain_action_details and brain_action_details.get('action') == 'pokemon_info':
            try:
                from rag_pokemon import get_pokemon_context
                poke_name = brain_action_details.get('pokemon_name')
                if poke_name:
                    context_str = get_pokemon_context(poke_name, text)
                    if context_str:
                        pokemon_context = context_str
                        logger.info(f"Injected Pokémon context for {poke_name}: {context_str[:60]}...")
            except Exception as e:
                logger.error(f"Error retrieving Pokémon context: {e}")
        
        # --- Type Effectiveness RAG Context (if requested by brain) ---
        type_effectiveness_context = None
        if brain_action_details and brain_action_details.get('action') == 'type_effectiveness':
            try:
                from rag_type_chart import get_type_effectiveness_context
                type_name = brain_action_details.get('type_name')
                if type_name:
                    context_str = get_type_effectiveness_context(type_name, text)
                    if context_str:
                        type_effectiveness_context = context_str
                        logger.info(f"Injected type effectiveness context for {type_name}: {context_str[:60]}...")
            except Exception as e:
                logger.error(f"Error retrieving type effectiveness context: {e}")

        logger.info("Proceeding with RAG and standard response generation.")
        
        # Mark decision end point (proceeding to response generation)
        mark_latency_timestamp("decision_end")

        # --- Pokemon RAG Context Only ---
        pokemon_context_text = "None"

        # --- Prepare History for Prompt ---
        # Use the same recent_raw_history slice defined earlier
        conversation_for_prompt = []
        for msg in recent_raw_history:
            role = msg.get("role", "user")
            content = msg.get("content", "").strip()
            if content: # Skip empty messages
                # For assistant messages, use "Luna"
                if role == "assistant":
                    speaker_label = "Luna"
                    conversation_for_prompt.append(f"{speaker_label}: {content}")
                else:
                    # For user messages, try to extract the name from the content
                    # The content format should be "Name: message"
                    name_match = re.match(r"^([^:]+):", content)
                    if name_match:
                        # Use the name from the content
                        speaker_label = name_match.group(1).strip()
                        # Extract just the message part
                        message_text = re.sub(r"^[^:]+:\s*", "", content)
                        conversation_for_prompt.append(f"{speaker_label}: {message_text}")
                    else:
                        # If no name in content, try to get canonical name from user ID
                        user_id = msg.get('user_id', 'unknown')
                        # Handle both string and int user_id
                        try:
                            user_id_int = int(user_id) if isinstance(user_id, str) else user_id
                            canonical_name = get_main_name_by_id(user_id_int)
                            if canonical_name:
                                speaker_label = canonical_name
                            else:
                                speaker_label = f"User_{user_id}"
                        except (ValueError, TypeError):
                            # If user_id can't be converted to int, use fallback
                            speaker_label = f"User_{user_id}"
                        conversation_for_prompt.append(f"{speaker_label}: {content}")

        # --- Prepare Final Prompt Parts ---
        # System instruction (passed separately to Vertex AI)
        final_system_prompt = system_prompt # Use the one passed in
        
        # Inject Pokemon and type effectiveness contexts into system prompt if available
        if pokemon_context:
            final_system_prompt += f"\n\n[Pokemon Context: {pokemon_context}]"
        if type_effectiveness_context:
            final_system_prompt += f"\n\n[Type Effectiveness: {type_effectiveness_context}]"

        # Construct the user message content, including context
        current_turn_user_content = ""

        # Check if this is a DM channel to determine if we should add context
        is_dm_channel = text_channel and hasattr(text_channel, 'type') and text_channel.type == discord.ChannelType.private

        # For DMs, use simple clean messages for better statefulness
        if is_dm_channel:
            # For DMs, use clean Gemma3 format
            current_turn_user_content = f"<start_of_turn>user\n{text}<end_of_turn>"
            logger.info("DM detected: Using clean Gemma3 format message content")
        else:
            # For guild channels, add full context as before
            
            # Build the context string
            context_parts = []
            
            # Build context as natural conversation context - no headers that could leak
            context_parts = []
            

            # Add background observations seamlessly if available
            try:
                from .auto_screenshot import auto_screenshot_manager
                recent_observations = auto_screenshot_manager.get_recent_observations(limit=3)
                if recent_observations:
                    obs_list = []
                    for obs in recent_observations:
                        obs_time = obs.get("timestamp", 0)
                        time_ago = int(time.time() - obs_time)
                        time_str = f"{time_ago}s ago" if time_ago < 60 else f"{time_ago//60}m ago"
                        obs_list.append(f"({time_str}) {obs.get('content', '')}")
                    context_parts.append(f"Recent activity: {'; '.join(obs_list)}")
                    logger.info(f"Added {len(recent_observations)} background observations to context")
            except ImportError:
                pass
            except Exception as e:
                logger.warning(f"Error adding background observations: {e}")
            
            # Add image analysis seamlessly if available
            if image_analysis_text:
                context_parts.append(f"Image description: {image_analysis_text}")
            
            # Add brain decision context seamlessly if available
            if brain_decision_context:
                context_parts.append(f"Background notes: {brain_decision_context}")
            
            # Special handling for voice context group conversations
            if is_voice_context and sink:
                # For voice group conversations, include recent conversation context
                recent_speakers = sink.get_recent_speaker_turns(count=5)
                if len(recent_speakers) > 0:
                    # Create a numbered list format for better LLM comprehension
                    conversation_flow = []
                    for i, speaker_turn in enumerate(recent_speakers, 1):
                        speaker_name = speaker_turn.get('display_name', f"User_{speaker_turn.get('user_id', 'unknown')}")
                        speaker_text = speaker_turn.get('text', '')
                        conversation_flow.append(f"{i}. {speaker_name}: {speaker_text}")
                    
                    if conversation_flow:
                        # Format as a clean numbered list with line breaks
                        formatted_conversation = '\n'.join(conversation_flow)
                        context_parts.append(f"Recent conversation:\n{formatted_conversation}")
                        logger.info(f"Added voice conversation context with {len(conversation_flow)} speakers in numbered format")
            
            # Resolve the effective display name to ensure consistency
            try:
                user_id_int = int(user_id) if isinstance(user_id, str) else user_id
                canonical_speaker_name = get_main_name_by_id(user_id_int)
                if canonical_speaker_name:
                    effective_display_name = canonical_speaker_name
                else:
                    # Keep the original effective_display_name if no canonical name found
                    pass
            except (ValueError, TypeError):
                # Keep the original effective_display_name if user_id conversion fails
                pass
            
            # Build a clean, simple user message without confusing context injection
            # Use the actual speaker's name and their direct message
            user_message_content = f"{effective_display_name}: {text}"
            
            # Wrap in proper Gemma3 conversation format with clear turn boundaries
            current_turn_user_content = f"<start_of_turn>user\n{user_message_content}<end_of_turn>"

        # OPTIMIZATION: Limit context size for faster TTFT
        MAX_CONTEXT_CHARS = 4000
        if len(current_turn_user_content) > MAX_CONTEXT_CHARS:
            logger.warning(f"Context too large ({len(current_turn_user_content)} chars), truncating to {MAX_CONTEXT_CHARS} for faster TTFT")
            current_turn_user_content = "..." + current_turn_user_content[-(MAX_CONTEXT_CHARS-3):]

        # --- Prepare History for Ollama ---
        # Clean up the message history formatting to use proper Gemma3 format
        history_messages = []

        # Add historical messages using the proper Gemma3 format with clear turn boundaries
        for msg in recent_raw_history:
             role = msg.get("role")
             content = msg.get("content", "").strip()
             if role and content:
                  # Clean the content to remove any existing format tags
                  clean_content = content
                  if clean_content.startswith("<start_of_turn>"):
                      # Extract just the actual message content
                      if "<end_of_turn>" in clean_content:
                          clean_content = clean_content.split("<start_of_turn>")[1].split("<end_of_turn>")[0]
                          if clean_content.startswith("user\n") or clean_content.startswith("model\n"):
                              clean_content = clean_content.split("\n", 1)[1] if "\n" in clean_content else clean_content
                      
                  if role == "assistant":
                      # Format assistant message with proper Gemma3 tags
                      formatted_content = f"<start_of_turn>model\n{clean_content}<end_of_turn>"
                  else:
                      # Format user message with proper Gemma3 tags  
                      formatted_content = f"<start_of_turn>user\n{clean_content}<end_of_turn>"
                      
                  history_messages.append({"role": role, "content": formatted_content})

        # --- Generation Config ---
        participation_count = sink.participation_counter.get(user_id, 0) if is_voice_context else 0
        dynamic_temp = min(DEFAULT_TEMP + (TEMP_PARTICIPATION_INCREMENT * participation_count), MAX_TEMP)
        generation_config = {
            "temperature": dynamic_temp,
            "max_tokens": MAX_OUTPUT_TOKENS,
            "top_p": TOP_P,
        }

        # --- Log Full Prompt to Console (if enabled) ---
        if os.environ.get("VERBOSE_PROMPT_LOGGING", "False").lower() == "true":
            try: # Try block for console logging
                print("\n--- LM STUDIO REQUEST ---")
                print(f"Model: {LM_STUDIO_MODEL_NAME}")
                print(f"Context: {'Voice (Streaming)' if is_voice_context and chat_session else ('Text (Stateful)' if chat_session else 'Text (Stateless)')}")
                print("\n--- System Prompt ---")
                print(final_system_prompt)
                print("\n--- History ---")
                for i, msg in enumerate(history_messages):
                    print(f"[{i}] {msg['role']}: {msg['content'][:100]}...")
                print("\n--- Current Turn User Content ---")
                print(current_turn_user_content)
                print("--- END REQUEST ---\n")
            except Exception as console_log_e: # Added except block
                logger.warning(f"Could not display full LM Studio prompt details to console: {console_log_e}")

        # --- Log Simplified Prompt to Discord Channel (if enabled) ---
        prompt_log_channel = bot.get_channel(PROMPT_LOG_CHANNEL_ID)
        if prompt_log_channel and os.environ.get("VERBOSE_PROMPT_LOGGING", "False").lower() == "true":
            try: # Try block for Discord logging
                log_content = ""
                log_title = ""

                # Determine context for title and content
                is_stateful = chat_session is not None
                context_type = ""
                if is_stateful:
                    context_type = "Voice Stream" if is_voice_context else "Text Stateful"
                    log_content = current_turn_user_content # Only log current turn content for stateful
                else: # Stateless fallback
                    context_type = "Text Stateless"
                    # Combine system prompt and history for stateless log
                    log_content = f"System Prompt:\n{final_system_prompt}\n\nHistory & Current Turn:\n"
                    # Format history messages
                    for msg in history_messages:
                        log_content += f"{msg['role']}: {msg['content']}\n"
                    # Add current turn
                    log_content += f"user: {current_turn_user_content}\n"

                log_title = f"**=== LM STUDIO PROMPT ({context_type}) ===**"
                full_log_message = f"{log_title}\n```\n{log_content.strip()}\n```"

                # Use create_task to avoid blocking, handle splitting
                async def send_log_message():
                    try:
                        if len(full_log_message) > 1950: # Adjust limit slightly for safety
                            await prompt_log_channel.send(log_title) # Send title first
                            content_chunks = [log_content[i:i+1900] for i in range(0, len(log_content), 1900)]
                            for chunk in content_chunks:
                                await prompt_log_channel.send(f"```\n{chunk.strip()}\n```")
                        else:
                            await prompt_log_channel.send(full_log_message)
                    except Exception as send_err:
                        logger.error(f"Failed to send prompt log to Discord channel {PROMPT_LOG_CHANNEL_ID}: {send_err}", exc_info=True)

                # Create the task to send the message
                asyncio.create_task(send_log_message())

            except Exception as discord_log_e: # Added except block
                logger.warning(f"Could not log prompt to Discord channel {PROMPT_LOG_CHANNEL_ID}: {discord_log_e}", exc_info=True)
        # --- End Discord Prompt Logging ---


        # --- Execute DM Action if Decided by Brain ---
        logger.info(f"DEBUG: DM variables - target='{dm_target_identifier_from_brain}', topic='{dm_topic_from_brain}', exact_message='{dm_exact_message_from_brain}'")
        if dm_target_identifier_from_brain and (dm_topic_from_brain or dm_exact_message_from_brain):
            logger.info(f"Attempting to execute brain-decided DM: Target='{dm_target_identifier_from_brain}', Topic='{dm_topic_from_brain}'")
            dm_handled = False
            target_user_for_dm = None
            try:
                # First resolve the identifier to get the user object for both exact messages and generated content
                guild_context = sink.voice_client.guild if sink and sink.voice_client else (effective_text_channel.guild if effective_text_channel else None)
                target_user_for_session = await find_user_from_identifier(
                    identifier=dm_target_identifier_from_brain,
                    bot=bot,
                    guild=guild_context
                )

                if not target_user_for_session:
                    logger.warning(f"Could not resolve user '{dm_target_identifier_from_brain}' for DM")
                    if effective_text_channel:
                        await effective_text_channel.send(f"❓ Couldn't find user '{dm_target_identifier_from_brain}' to send DM.")
                    return  # Skip this DM attempt

                # --- Determine DM Content ---
                dm_content_to_send = None
                if dm_exact_message_from_brain:
                    # Use the exact message provided
                    dm_content_to_send = dm_exact_message_from_brain
                    logger.info(f"Using exact message for DM: '{dm_content_to_send}'")
                else:
                    # Generate DM content using Ollama for topic-based requests
                    # Construct a prompt for the main LLM to generate the actual DM message
                    # Use the main system prompt for personality, plus specific instructions
                    dm_generation_prompt = f"""{final_system_prompt}

---
You need to write a short, casual Direct Message (DM).
The intended recipient is identified as: '{dm_target_identifier_from_brain}' (resolve this to their main name if known).
The reason or topic for this DM is: "{dm_topic_from_brain}".

Based ONLY on this topic and your personality, generate the DM message content.
Keep it concise and natural-sounding. Do NOT add greetings like "Hey [Name]," unless it flows very naturally with the topic.
Focus on conveying the core topic: "{dm_topic_from_brain}".
Generate ONLY the DM message content below:
"""
                    # Use Ollama client for DM generation
                    ollama_client = get_ollama_client()
                    logger.info(f"DEBUG: Ollama client for DM generation: {ollama_client}")
                
                if dm_content_to_send:
                    # Already have the exact message, proceed to send it
                    logger.info(f"Using exact message for DM to '{dm_target_identifier_from_brain}': '{dm_content_to_send}'")
                elif ollama_client:
                    # Generate content for topic-based requests
                    logger.info(f"Generating DM content for target '{dm_target_identifier_from_brain}' about '{dm_topic_from_brain}'...")
                    dm_gen_start_time = time.monotonic()

                    # Get or create DM chat session using the user ID for consistency
                    dm_session_key = f"dm_{target_user_for_session.id}"
                    dm_session = bot.chat_sessions.get(dm_session_key)

                    if dm_session is None:
                        logger.info(f"Creating new DM chat session for user {target_user_for_session.id}")
                        dm_session = {
                            "id": f"dm_{target_user_for_session.id}_{int(time.time())}",
                            "messages": []  # No system prompt for Ollama - using custom modelfile instead
                        }
                        bot.chat_sessions[dm_session_key] = dm_session

                    # Simplified prompt for DM generation
                    simple_dm_prompt = f"You need to send a DM about: '{dm_topic_from_brain}'. Generate a short, casual message:"

                    # Use the DM session messages and add the new prompt
                    messages = dm_session["messages"].copy()
                    messages.append({"role": "user", "content": simple_dm_prompt})

                    # Make the optimized API call using direct aiohttp
                    dm_response_text = await _optimized_ollama_non_streaming_call(
                        messages=messages,
                        model=OLLAMA_MODEL_NAME,
                        temperature=DM_TEMP,
                        max_tokens=DM_MAX_OUTPUT_TOKENS,
                        bot=bot
                    )

                    dm_gen_end_time = time.monotonic()
                    logger.info(f"DM content generation took {dm_gen_end_time - dm_gen_start_time:.4f}s")

                    if not dm_response_text or not dm_response_text.strip():
                         logger.warning(f"LLM DM content generation failed or was empty.")
                         if effective_text_channel: await effective_text_channel.send(f"⚠️ Tried to DM '{dm_target_identifier_from_brain}' about '{dm_topic_from_brain}', but couldn't generate content.")
                    else:
                         dm_content_to_send = dm_response_text.strip()
                         if not dm_content_to_send:
                              logger.warning(f"LLM generated empty DM content for topic: {dm_topic_from_brain}")
                              if effective_text_channel: await effective_text_channel.send(f"⚠️ Tried to DM '{dm_target_identifier_from_brain}' about '{dm_topic_from_brain}', but couldn't think of what to say!")
                
                # --- Send DM using the already resolved user ---
                if dm_content_to_send and target_user_for_session:
                    logger.info(f"Sending DM to {target_user_for_session.name} ({target_user_for_session.id})")
                    target_user_for_dm = target_user_for_session  # Use the already resolved user

                    if target_user_for_dm:
                         try:
                              # Get or create DM session using the target user's ID (consistent with main.py)
                              dm_session_key = f"dm_{target_user_for_dm.id}"
                              dm_session = bot.chat_sessions.get(dm_session_key)

                              if dm_session is None:
                                  logger.info(f"Creating new DM chat session for user {target_user_for_dm.id}")
                                  dm_session = {
                                      "id": f"dm_{target_user_for_dm.id}_{int(time.time())}",
                                      "messages": []  # No system prompt for Ollama - using custom modelfile instead
                                  }
                                  bot.chat_sessions[dm_session_key] = dm_session

                              # Update DM session with appropriate content
                              if dm_exact_message_from_brain:
                                  # For exact messages, log as direct message relay
                                  dm_session["messages"].append({"role": "user", "content": f"Relay message: {dm_exact_message_from_brain}"})
                                  dm_session["messages"].append({"role": "assistant", "content": dm_content_to_send})
                              else:
                                  # For generated messages, use the existing session structure
                                  dm_session["messages"].append({"role": "user", "content": simple_dm_prompt})
                                  dm_session["messages"].append({"role": "assistant", "content": dm_content_to_send})

                              await target_user_for_dm.send(dm_content_to_send)
                              logger.info(f"Successfully sent DM to {target_user_for_dm.name} ({target_user_for_dm.id})")
                              
                              # Log to DB with appropriate description
                              if dm_exact_message_from_brain:
                                  log_description = f"Sent DM to {target_user_for_dm.name} ({target_user_for_dm.id}) with exact message: {dm_content_to_send}"
                              else:
                                  log_description = f"Sent DM to {target_user_for_dm.name} ({target_user_for_dm.id}) about '{dm_topic_from_brain}': {dm_content_to_send}"
                              
                              log_message_to_db(
                                   user_id=str(bot.user.id), role="assistant_dm",
                                   content=log_description,
                                   timestamp=time.time(), channel_type="dm", channel_id=str(target_user_for_dm.id)
                              )
                              dm_handled = True
                         except discord.Forbidden:
                              logger.warning(f"Failed to send DM to {target_user_for_dm.name}: Permissions or privacy settings.")
                              if effective_text_channel: await effective_text_channel.send(f"⚠️ Couldn't send DM to {target_user_for_dm.mention} (maybe privacy settings?).")
                              dm_handled = True # Still handled, just failed sending
                         except discord.HTTPException as e_dm_send:
                              logger.error(f"Failed to send DM to {target_user_for_dm.name} due to API error: {e_dm_send}")
                              if effective_text_channel: await effective_text_channel.send(f"❌ Failed to send DM to {target_user_for_dm.mention} due to a Discord error.")
                              dm_handled = True # Still handled, just failed sending
                    else:
                         logger.warning(f"Could not find user matching '{dm_target_identifier_from_brain}' to send DM.")
                         if effective_text_channel: await effective_text_channel.send(f"❓ Brain wanted to DM '{dm_target_identifier_from_brain}', but I couldn't find them.")
                         dm_handled = True # Handled (finding failed)

                else:
                     logger.error("Cannot generate DM content: Ollama client not available.")
                     if effective_text_channel: await effective_text_channel.send("❌ Cannot generate DM content: Ollama client not available.")
                     dm_handled = True # Handled (generation failed)

            except Exception as e_dm_proc:
                logger.error(f"Unexpected error during DM processing for target '{dm_target_identifier_from_brain}': {e_dm_proc}", exc_info=True)
                if effective_text_channel: await effective_text_channel.send(f"❌ An unexpected error occurred while trying to process the DM for '{dm_target_identifier_from_brain}'.")
                dm_handled = True # Mark as handled even on unexpected error

            if dm_handled:
                logger.info("DM action processed (or failed). Skipping standard response.")
                return True # Indicate turn was handled by DM attempt

        # --- If not DMing, Execute Standard API Call ---
        # (Original code for voice/text responses follows)
        try: # Try block for API calls
            # Case 1: Stateful Voice (Sink and Session exist)
            if sink and chat_session:
                logger.info("Generating STATEFUL VOICE response (Streaming)")
                voice_client = sink.voice_client
                if not voice_client:
                    logger.error("ChatSession provided, but no active voice client found in sink!")
                    return False

                if voice_client.is_playing(): voice_client.stop()
                sink.is_speaking = True
                
                # Check if VTube Studio synchronization should be enabled
                vts_sync_enabled = False
                try:
                    from llm_response.config import VTUBE_STUDIO_ENABLED
                    vts_sync_enabled = VTUBE_STUDIO_ENABLED
                except:
                    pass
                
                player = StreamingSpeechPlayer(
                    voice_client, 
                    after_play_callback=lambda e: sink._after_play(e, user_id),
                    enable_vts_sync=vts_sync_enabled
                )
                await player.start()
                mark_latency_timestamp("tts_player_start")

                full_response_text = ""
                mark_latency_timestamp("llm_api_start")
                logger.info(f"STATEFUL OLLAMA STREAMING RESPONSE API CALL ({OLLAMA_MODEL_NAME}): Starting...")

                # Prepare messages for the API call
                # Copy the messages from the session and add our new prompt
                messages = []
                if isinstance(chat_session, dict) and "messages" in chat_session:
                    # Make a deep copy of the messages to avoid modifying the original
                    all_session_messages = chat_session["messages"].copy()

                    # Use full session messages for complete conversation history
                    max_session_messages = FAST_SESSION_LIMIT  # Use config value for full conversation history
                    if len(all_session_messages) > max_session_messages:
                        # Keep the most recent messages
                        messages = all_session_messages[-max_session_messages:]
                        logger.info(f"Truncated session messages from {len(all_session_messages)} to {len(messages)} for context management")
                    else:
                        messages = all_session_messages

                    # Log the messages being used for the API call
                    logger.info(f"Using {len(messages)} messages from chat session for Ollama API call")
                    
                    # Log context size for TTFT optimization
                    total_context_chars = sum(len(msg.get('content', '')) for msg in messages) + len(current_turn_user_content)
                    logger.info(f"Total context size: {total_context_chars} characters")
                    
                    for i, msg in enumerate(messages):
                        logger.info(f"Message {i}: role={msg.get('role')}, content={msg.get('content', '')[:50]}...")
                else:
                    # Create a new message list (no system prompt for Ollama - using custom modelfile)
                    logger.info("No valid chat session found, creating new message list without system prompt (using Ollama custom modelfile)")

                # Inject Pokémon context if available
                if pokemon_context:
                    messages.insert(0, {"role": "system", "content": pokemon_context})
                # Inject type effectiveness context if available
                if type_effectiveness_context:
                    messages.insert(0, {"role": "system", "content": type_effectiveness_context})
                # Add the current user message
                messages.append({"role": "user", "content": current_turn_user_content})
                # Add the model start tag to cue the model to respond with proper Gemma3 format
                messages.append({"role": "assistant", "content": "<start_of_turn>model\n"})
                logger.info(f"Added current user message and model cue to API call messages. Now has {len(messages)} messages")

                try: # Inner try for stream processing
                    # Mark LLM API start timing
                    mark_latency_timestamp("llm_api_start")
                    
                    # Make the optimized streaming API call using direct aiohttp
                    first_chunk_received = False

                    # Process the streaming response
                    try:
                        async for chunk_text in _ultra_fast_ollama_streaming_call(
                            messages=messages,
                            model=OLLAMA_MODEL_NAME,
                            temperature=generation_config["temperature"],
                            max_tokens=generation_config["max_tokens"],
                            top_p=generation_config["top_p"],
                            bot=bot
                        ):
                            if not first_chunk_received:
                                mark_latency_timestamp("first_token")
                                first_chunk_received = True

                            if chunk_text:
                                full_response_text += chunk_text
                                await player.add_text(chunk_text) # Feed chunk to TTS player

                            # Yield control briefly, allowing other tasks to run
                            await asyncio.sleep(0.001) # Smaller sleep might improve responsiveness slightly
                    except Exception as e:
                        logger.error(f"Error processing stream chunk: {e}", exc_info=True)

                    # Removed debug subprocess calls for better performance

                except Exception as stream_err:
                    logger.error(f"Error during LM Studio stateful stream processing: {stream_err}", exc_info=True)
                    await player.add_text("Sorry, something went wrong while I was thinking.")
                finally:
                     await player.finalize() # Ensure player is always finalized
                     mark_latency_timestamp("llm_api_end")

                if full_response_text:
                    clean_response = remove_emojis(full_response_text.strip())
                    # Set response length for latency tracking
                    set_response_length(len(clean_response))
                    # Meta-commentary removal (optional)
                    # ... (patterns omitted for brevity) ...
                    if clean_response:
                        logger.info(f"Luna Voice Response (Full Streamed Text): {clean_response}")
                        if hasattr(sink, '_add_to_history'):
                            await sink._add_to_history("assistant", clean_response, bot.user.id, None, "voice", voice_client.channel.id if voice_client else None)
                        # Log to transcript channel
                        log_channel = bot.get_channel(TRANSCRIPT_LOG_CHANNEL_ID)
                        if log_channel: asyncio.create_task(log_channel.send(f"**Luna:** {clean_response}"))

                        # --- VTube Studio Integration ---
                        # Note: VTube Studio TTS and expressions are now handled synchronously 
                        # in StreamingSpeechPlayer when enable_vts_sync=True
                        # This ensures perfect timing alignment with Discord TTS
                        logger.debug("🎭 VTube Studio operations handled synchronously via StreamingSpeechPlayer")
                        # --- End VTube Studio Integration ---

                        # Update the chat session with the assistant's response
                        if isinstance(chat_session, dict) and "messages" in chat_session:
                            # Check if the user message is already in the session (avoid duplicates)
                            # Note: current_turn_user_content already has proper Gemma3 format
                            if not any(msg.get("role") == "user" and msg.get("content") == current_turn_user_content for msg in chat_session["messages"]):
                                chat_session["messages"].append({"role": "user", "content": current_turn_user_content})
                                logger.info(f"Added user message to voice chat session")
                            else:
                                logger.info(f"User message already exists in voice chat session, skipping")

                            # Add the assistant's response with proper Gemma3 format
                            formatted_response = f"<start_of_turn>model\n{clean_response}<end_of_turn>"
                            chat_session["messages"].append({"role": "assistant", "content": formatted_response})
                            logger.info(f"Added assistant response to voice chat session. Session now has {len(chat_session['messages'])} messages.")

                            # Implement session truncation to prevent indefinite growth
                            max_stored_messages = MAX_STORED_MESSAGES  # Use config value for storage limit
                            if len(chat_session["messages"]) > max_stored_messages:
                                # Remove oldest messages, keeping the most recent ones
                                messages_to_remove = len(chat_session["messages"]) - max_stored_messages
                                chat_session["messages"] = chat_session["messages"][messages_to_remove:]
                                logger.info(f"Truncated voice chat session from {len(chat_session['messages']) + messages_to_remove} to {len(chat_session['messages'])} messages")

                            # Log the first few messages in the session for debugging
                            if len(chat_session["messages"]) > 0:
                                logger.info(f"First message in voice session: role={chat_session['messages'][0].get('role')}, content={chat_session['messages'][0].get('content')[:50]}...")
                            if len(chat_session["messages"]) > 1:
                                logger.info(f"Second message in voice session: role={chat_session['messages'][1].get('role')}, content={chat_session['messages'][1].get('content')[:50]}...")

                            # Memory storage disabled
                    else: logger.warning("Full streamed voice response was empty after cleaning.")
                else: logger.warning("Full streamed voice response was empty.")

                # Memory storage disabled

                sink.participation_counter[user_id] = sink.participation_counter.get(user_id, 0) + 1

                # --- After generating Luna's response, check for Minecraft command ---
                # (Insert this after clean_response is set in each response block)
                # Only do this for text/voice, not DMs
                if not is_dm_channel and is_minecraft_mode_enabled() and mc_bridge is not None:
                    command = extract_minecraft_command(clean_response)
                    if not command:
                        # Fallback: try to translate the user request to a command
                        ollama_client = get_ollama_client()
                        translated = await llm_translate_to_command(text, ollama_client, OLLAMA_MODEL_NAME)
                        command = extract_minecraft_command(translated or "")
                        if command:
                            logger.info(f"[MC Fallback] Sending translated command to Minecraft: {command}")
                            mc_bridge.send_command(command)
                        else:
                            logger.info("[MC Fallback] No valid Minecraft command found after second LLM call.")
                    else:
                        logger.info(f"[MC] Sending command to Minecraft: {command}")
                        mc_bridge.send_command(command)

                return True

            # Case 2: Stateful Text (Text Channel and Session exist)
            elif effective_text_channel and chat_session:
                logger.info(f"Generating STATEFUL TEXT response for channel {effective_text_channel.id}")
                response_start_time = time.monotonic()

                # Prepare messages for the API call
                messages = []
                if isinstance(chat_session, dict) and "messages" in chat_session:
                    # Make a deep copy of the messages to avoid modifying the original
                    all_session_messages = chat_session["messages"].copy()

                    # Use full session messages for complete conversation history
                    max_session_messages = FAST_SESSION_LIMIT  # Use config value for full conversation history
                    if len(all_session_messages) > max_session_messages:
                        # Keep the most recent messages
                        messages = all_session_messages[-max_session_messages:]
                        logger.info(f"Truncated session messages from {len(all_session_messages)} to {len(messages)} for context management")
                    else:
                        messages = all_session_messages

                    # Log the messages being used for the API call
                    logger.info(f"Using {len(messages)} messages from chat session for Ollama API call")
                    
                    # Log context size for TTFT optimization
                    total_context_chars = sum(len(msg.get('content', '')) for msg in messages) + len(current_turn_user_content)
                    logger.info(f"Total context size: {total_context_chars} characters")
                    
                    for i, msg in enumerate(messages):
                        logger.info(f"Message {i}: role={msg.get('role')}, content={msg.get('content', '')[:50]}...")
                else:
                    # Create a new message list (no system prompt for Ollama - using custom modelfile)
                    logger.info(f"Created new messages list without system prompt (using Ollama custom modelfile)")

                # Inject Pokémon context if available
                if pokemon_context:
                    messages.insert(0, {"role": "system", "content": pokemon_context})
                # Inject type effectiveness context if available
                if type_effectiveness_context:
                    messages.insert(0, {"role": "system", "content": type_effectiveness_context})
                # Add the current user message
                messages.append({"role": "user", "content": current_turn_user_content})
                # Add the model start tag to cue the model to respond with proper Gemma3 format
                messages.append({"role": "assistant", "content": "<start_of_turn>model\n"})
                logger.info(f"Added user message and model cue to messages list. Now has {len(messages)} messages")

                # Make the optimized API call using direct aiohttp
                response_text = await _optimized_ollama_non_streaming_call(
                    messages=messages,
                    model=OLLAMA_MODEL_NAME,
                    temperature=generation_config["temperature"],
                    max_tokens=generation_config["max_tokens"],
                    top_p=generation_config["top_p"],
                    bot=bot
                )

                response_end_time = time.monotonic()
                total_api_duration = response_end_time - response_start_time
                logger.info(f"STATEFUL OLLAMA TEXT RESPONSE API CALL ({OLLAMA_MODEL_NAME}): Completed in {total_api_duration:.6f}s")
                await log_latency_to_discord(bot, f"Ollama Stateful Text: {total_api_duration:.4f}s")

                clean_response = remove_emojis(response_text.strip()) if response_text else ""
                if clean_response:
                    await effective_text_channel.send(clean_response)
                    log_message_to_db(bot.user.id, "assistant", clean_response, time.time(), "text", effective_text_channel.id)
                    logger.info(f"Luna Stateful Text Response (Channel {effective_text_channel.id}): {clean_response}")
                    log_channel = bot.get_channel(TRANSCRIPT_LOG_CHANNEL_ID)
                    if log_channel: asyncio.create_task(log_channel.send(f"**Luna:** {clean_response}"))

                    # --- VTube Studio Integration ---
                    # Trigger VTS expressions and local TTS concurrently without blocking Discord response
                    try:
                        import vtube_studio_client
                        import vtube_studio_tts
                        import vtube_studio_emotions
                        from llm_response.config import VTUBE_STUDIO_ENABLED, VTUBE_STUDIO_AUDIO_DEVICE
                        
                        if VTUBE_STUDIO_ENABLED:
                            # Create concurrent tasks for VTS operations
                            async def vts_operations():
                                try:
                                    # Detect emotion and trigger expression
                                    hotkey_id = vtube_studio_emotions.detect_emotion_for_vts(clean_response)
                                    if hotkey_id:
                                        await vtube_studio_client.trigger_hotkey(hotkey_id)
                                    
                                    # Speak locally for VTS lip-sync
                                    await vtube_studio_tts.speak_locally_for_vts(clean_response, VTUBE_STUDIO_AUDIO_DEVICE)
                                except Exception as e:
                                    logger.error(f"❌ Error in VTube Studio operations: {e}", exc_info=True)
                            
                            # Run VTS operations concurrently without blocking
                            asyncio.create_task(vts_operations())
                            logger.debug("🎭 VTube Studio operations started in background")
                    except Exception as e:
                        logger.error(f"❌ Error starting VTube Studio integration: {e}")
                    # --- End VTube Studio Integration ---

                    # Memory storage disabled

                    # Update the session with the assistant's response
                    if isinstance(chat_session, dict) and "messages" in chat_session:
                        # Check if the user message is already in the session (avoid duplicates)
                        if not any(msg.get("role") == "user" and msg.get("content") == current_turn_user_content for msg in chat_session["messages"]):
                            chat_session["messages"].append({"role": "user", "content": current_turn_user_content})
                            logger.info(f"Added user message to chat session")
                        else:
                            logger.info(f"User message already exists in chat session, skipping")

                        # Add the assistant's response with proper Gemma3 format
                        formatted_response = f"<start_of_turn>model\n{clean_response}<end_of_turn>"
                        chat_session["messages"].append({"role": "assistant", "content": formatted_response})
                        logger.info(f"Added assistant response to chat session. Session now has {len(chat_session['messages'])} messages.")

                        # Implement session truncation to prevent indefinite growth
                        max_stored_messages = MAX_STORED_MESSAGES  # Use config value for storage limit
                        if len(chat_session["messages"]) > max_stored_messages:
                            # Remove oldest messages, keeping the most recent ones
                            messages_to_remove = len(chat_session["messages"]) - max_stored_messages
                            chat_session["messages"] = chat_session["messages"][messages_to_remove:]
                            logger.info(f"Truncated text chat session from {len(chat_session['messages']) + messages_to_remove} to {len(chat_session['messages'])} messages")

                        # Log the first few messages in the session for debugging
                        if len(chat_session["messages"]) > 0:
                            logger.info(f"First message in session: role={chat_session['messages'][0].get('role')}, content={chat_session['messages'][0].get('content')[:50]}...")
                        if len(chat_session["messages"]) > 1:
                            logger.info(f"Second message in session: role={chat_session['messages'][1].get('role')}, content={chat_session['messages'][1].get('content')[:50]}...")
                else:
                    logger.warning("Stateful text response was empty.")

                # --- After generating Luna's response, check for Minecraft command ---
                # (Insert this after clean_response is set in each response block)
                # Only do this for text/voice, not DMs
                if not is_dm_channel and is_minecraft_mode_enabled() and mc_bridge is not None:
                    command = extract_minecraft_command(clean_response)
                    if not command:
                        # Fallback: try to translate the user request to a command
                        ollama_client = get_ollama_client()
                        translated = await llm_translate_to_command(text, ollama_client, OLLAMA_MODEL_NAME)
                        command = extract_minecraft_command(translated or "")
                        if command:
                            logger.info(f"[MC Fallback] Sending translated command to Minecraft: {command}")
                            mc_bridge.send_command(command)
                        else:
                            logger.info("[MC Fallback] No valid Minecraft command found after second LLM call.")
                    else:
                        logger.info(f"[MC] Sending command to Minecraft: {command}")
                        mc_bridge.send_command(command)

                return True

            # Case 3: Stateless Text (Text Channel exists, Session is None) - Fallback
            elif effective_text_channel:
                logger.warning(f"Generating STATELESS TEXT response (Fallback) for channel {effective_text_channel.id}")
                response_start_time = time.monotonic()

                # Prepare messages for the API call (no system prompt for Ollama - using custom modelfile)
                messages = []
                logger.info("Preparing stateless messages list without system prompt (using Ollama custom modelfile)")

                # Add historical messages
                for msg in history_messages:
                    if msg["role"] != "system":  # Skip system messages (not using them for Ollama)
                        messages.append(msg)
                logger.info(f"Added {len(history_messages)} historical messages to stateless messages list")

                # Inject Pokémon context if available
                if pokemon_context:
                    messages.insert(0, {"role": "system", "content": pokemon_context})
                # Inject type effectiveness context if available
                if type_effectiveness_context:
                    messages.insert(0, {"role": "system", "content": type_effectiveness_context})
                # Add the current user message (if not already in the messages)
                if not any(msg.get("role") == "user" and msg.get("content") == current_turn_user_content for msg in messages):
                    messages.append({"role": "user", "content": current_turn_user_content})
                    # Add the model start tag to cue the model to respond with proper Gemma3 format
                    messages.append({"role": "assistant", "content": "<start_of_turn>model\n"})
                    logger.info("Added current user message and model cue to stateless messages list")

                # Log the final message list
                logger.info(f"Final stateless messages list has {len(messages)} messages")
                for i, msg in enumerate(messages):
                    logger.info(f"Stateless Message {i}: role={msg.get('role')}, content={msg.get('content', '')[:50]}...")

                # Make the optimized API call using direct aiohttp
                response_text = await _ultra_fast_ollama_non_streaming_call(
                    messages=messages,
                    model=OLLAMA_MODEL_NAME,
                    temperature=generation_config["temperature"],
                    max_tokens=generation_config["max_tokens"],
                    top_p=generation_config["top_p"],
                    bot=bot
                )

                response_end_time = time.monotonic()
                total_api_duration = response_end_time - response_start_time
                logger.info(f"STATELESS OLLAMA TEXT RESPONSE API CALL ({OLLAMA_MODEL_NAME}): Completed in {total_api_duration:.6f}s")
                await log_latency_to_discord(bot, f"Ollama Stateless Text: {total_api_duration:.4f}s")

                clean_response = remove_emojis(response_text.strip()) if response_text else ""
                if clean_response:
                    await effective_text_channel.send(clean_response)
                    log_message_to_db(bot.user.id, "assistant", clean_response, time.time(), "text", effective_text_channel.id)
                    logger.info(f"Luna Stateless Text Response (Channel {effective_text_channel.id}): {clean_response}")
                    log_channel = bot.get_channel(TRANSCRIPT_LOG_CHANNEL_ID)
                    if log_channel: asyncio.create_task(log_channel.send(f"**Luna:** {clean_response}"))

                    # --- VTube Studio Integration ---
                    # Trigger VTS expressions and local TTS concurrently without blocking Discord response
                    try:
                        import vtube_studio_client
                        import vtube_studio_tts
                        import vtube_studio_emotions
                        from llm_response.config import VTUBE_STUDIO_ENABLED, VTUBE_STUDIO_AUDIO_DEVICE
                        
                        if VTUBE_STUDIO_ENABLED:
                            # Create concurrent tasks for VTS operations
                            async def vts_operations():
                                try:
                                    # Detect emotion and trigger expression
                                    hotkey_id = vtube_studio_emotions.detect_emotion_for_vts(clean_response)
                                    if hotkey_id:
                                        await vtube_studio_client.trigger_hotkey(hotkey_id)
                                    
                                    # Speak locally for VTS lip-sync
                                    await vtube_studio_tts.speak_locally_for_vts(clean_response, VTUBE_STUDIO_AUDIO_DEVICE)
                                except Exception as e:
                                    logger.error(f"❌ Error in VTube Studio operations: {e}", exc_info=True)
                            
                            # Run VTS operations concurrently without blocking
                            asyncio.create_task(vts_operations())
                            logger.debug("🎭 VTube Studio operations started in background")
                    except Exception as e:
                        logger.error(f"❌ Error starting VTube Studio integration: {e}")
                    # --- End VTube Studio Integration ---

                    # Memory storage disabled
                else:
                    logger.warning("Stateless text response was empty.")

                # --- After generating Luna's response, check for Minecraft command ---
                # (Insert this after clean_response is set in each response block)
                # Only do this for text/voice, not DMs
                if not is_dm_channel and is_minecraft_mode_enabled() and mc_bridge is not None:
                    command = extract_minecraft_command(clean_response)
                    if not command:
                        # Fallback: try to translate the user request to a command
                        ollama_client = get_ollama_client()
                        translated = await llm_translate_to_command(text, ollama_client, OLLAMA_MODEL_NAME)
                        command = extract_minecraft_command(translated or "")
                        if command:
                            logger.info(f"[MC Fallback] Sending translated command to Minecraft: {command}")
                            mc_bridge.send_command(command)
                        else:
                            logger.info("[MC Fallback] No valid Minecraft command found after second LLM call.")
                    else:
                        logger.info(f"[MC] Sending command to Minecraft: {command}")
                        mc_bridge.send_command(command)

                return True

            # Case 4: No valid context
            else:
                logger.error("Reached response generation block but no valid context (sink/session or text channel) was found.")
                return False

        except Exception as e: # Catch errors during API call/response handling
            logger.error(f"Error during response generation API call: {e}", exc_info=True)
            error_message = "Sorry, I encountered an error while trying to respond."
            if sink: await sink.play_error_message(error_message)
            elif effective_text_channel: await effective_text_channel.send(error_message)
            return False # Indicate failure

    except Exception as e: # Catch errors in the outer function scope
         logger.error(f"Outer processing error in process_user_message: {e}", exc_info=True)
         try: # Attempt to report error
             final_effective_text_channel = text_channel or (sink.text_channel if sink else None)
             if final_effective_text_channel: await final_effective_text_channel.send("😵 Uh oh, something went wrong internally.")
         except Exception as report_err: logger.error(f"Failed to send outer error message: {report_err}")
         return False # Indicate failure
    finally:
        # Always resume auto-screenshots when response generation is complete
        if _auto_screenshot_available and auto_screenshot:
            auto_screenshot.auto_screenshot_manager.resume_after_response()

def extract_minecraft_command(text):
    """
    Returns the first valid Minecraft command (e.g., !craft(...)) in the text, or None if not found.
    """
    match = re.search(r"!(\w+)(?:\([^)]*\))?", text)
    if match:
        return match.group(0)
    return None

VALID_MINDCRAFT_COMMANDS = [
    '!craftRecipe', '!equip', '!givePlayer', '!followPlayer', '!goToPlayer', '!takeFromChest', '!putInChest', '!collectBlocks', '!attack', '!consume', '!goal', '!stop', '!goToCoordinates', '!searchForBlock', '!searchForEntity', '!moveAway', '!rememberHere', '!goToRememberedPlace', '!discard', '!smeltItem', '!clearFurnace', '!placeHere', '!goToBed', '!activate', '!stay', '!setMode', '!endGoal', '!startConversation', '!endConversation', '!lookAtPlayer', '!viewChest', '!entities', '!inventory', '!craftable', '!getCraftingPlan', '!searchWiki', '!modes', '!savedPlaces', '!newAction', '!restart', '!clearChat', '!stfu'
]

async def llm_translate_to_command(user_request, ollama_client, model, temperature=0.2, max_tokens=64):
    """
    Calls the LLM to translate a user request into a Mindcraft command string.
    """
    command_list = ', '.join(VALID_MINDCRAFT_COMMANDS)
    prompt = (
        "You are an expert at controlling a Minecraft agent using Mindcraft commands. "
        f"Only use one of these commands: {command_list}. "
        "If the user request cannot be mapped to a valid command, reply with 'NO_COMMAND'. "
        "Convert the following user request into a single valid Mindcraft command. "
        "Only output the command, nothing else.\n"
        f"User request: {user_request}"
    )
    messages = [
        {"role": "user", "content": prompt}
    ]
    result = await _optimized_ollama_non_streaming_call(
        messages=messages,
        model=model,
        temperature=temperature,
        max_tokens=max_tokens,
        bot=None  # No bot instance available in this context
    )
    if result:
        result = result.strip()
        # Only allow commands in the whitelist
        if any(result.startswith(cmd) for cmd in VALID_MINDCRAFT_COMMANDS):
            return result
        if result == 'NO_COMMAND':
            return None
        logger.warning(f"[MC Fallback] LLM output not in whitelist: {result}")
        return None
    return None
